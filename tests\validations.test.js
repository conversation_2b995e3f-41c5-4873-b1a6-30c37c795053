const { GraphQLError } = require('graphql');
const {
  validateNonEmptyString,
  validateDateOfBirth,
  validateTaskPriority,
  validateClearanceLevel,
  validateAgentRole,
  validateCaseStatus,
  validateInformantExists,
} = require('../validations');

process.env.JWT_SECRET = 'test-secret';
const { resolvers } = require('../resolvers');

describe('validateNonEmptyString', () => {
  test('throws GraphQLError for empty string', () => {
    expect(() => validateNonEmptyString('', 'field')).toThrow(GraphQLError);
  });

  test('does not throw for valid string', () => {
    expect(() => validateNonEmptyString('valid', 'field')).not.toThrow();
  });

  test('throws GraphQLError for whitespace only string', () => {
    expect(() => validateNonEmptyString('  ', 'field')).toThrow(GraphQLError);
  });

  test('throws GraphQLError for non-string value', () => {
    expect(() => validateNonEmptyString(123, 'field')).toThrow(GraphQLError);
  });
});

describe('validateDateOfBirth', () => {
  test('throws GraphQLError for too early date', () => {
    expect(() => validateDateOfBirth('1800-01-01')).toThrow(GraphQLError);
  });

  test('accepts valid date string', () => {
    expect(() => validateDateOfBirth('2000-01-01')).not.toThrow();
  });

  test('throws GraphQLError for future date', () => {
    const future = new Date();
    future.setFullYear(future.getFullYear() + 1);
    const futureStr = future.toISOString().split('T')[0];
    expect(() => validateDateOfBirth(futureStr)).toThrow(GraphQLError);
  });

  test('throws GraphQLError for malformed date string', () => {
    expect(() => validateDateOfBirth('not-a-date')).toThrow(GraphQLError);
  });

  test('accepts valid date object', () => {
    expect(() => validateDateOfBirth({ year: 2000, month: 1, day: 1 })).not.toThrow();
  });
});

describe('validateTaskPriority', () => {
  test('throws GraphQLError for invalid priority', () => {
    expect(() => validateTaskPriority('INVALID')).toThrow(GraphQLError);
  });

  test('does not throw for valid priority', () => {
    expect(() => validateTaskPriority('HIGH')).not.toThrow();
  });
});

describe('validateClearanceLevel', () => {
  test('throws GraphQLError for invalid clearance level', () => {
    expect(() => validateClearanceLevel('CL0')).toThrow(GraphQLError);
  });

  test('does not throw for valid clearance level', () => {
    expect(() => validateClearanceLevel('CL1')).not.toThrow();
  });
});

describe('validateAgentRole', () => {
  test('throws GraphQLError for invalid agent role', () => {
    expect(() => validateAgentRole('MANAGER')).toThrow(GraphQLError);
  });

  test('does not throw for valid agent role', () => {
    expect(() => validateAgentRole('ADMIN')).not.toThrow();
  });
});

describe('validateCaseStatus', () => {
  test('throws GraphQLError for invalid case status', () => {
    expect(() => validateCaseStatus('IN_PROGRESS')).toThrow(GraphQLError);
  });

  test('does not throw for valid case status', () => {
    expect(() => validateCaseStatus('OPEN')).not.toThrow();
  });
});

describe('validateInformantExists', () => {
  const createMockDriver = (records) => ({
    session: jest.fn(() => ({
      run: jest.fn().mockResolvedValue({ records }),
      close: jest.fn(),
    })),
  });

  test('throws GraphQLError when no informant found', async () => {
    const driver = createMockDriver([]);
    await expect(validateInformantExists(driver, '1')).rejects.toThrow(GraphQLError);
  });

  test('resolves when informant exists', async () => {
    const driver = createMockDriver([{ get: jest.fn() }]);
    await expect(validateInformantExists(driver, '1')).resolves.not.toThrow();
  });
});

describe('updatePerson resolver', () => {
  const createMockDriver = () => ({
    session: jest.fn(() => ({
      run: jest.fn().mockResolvedValue({ records: [{ get: jest.fn() }] }),
      close: jest.fn(),
    })),
  });

  test('throws GraphQLError for empty firstName', async () => {
    const driver = createMockDriver();
    await expect(
      resolvers.Mutation.updatePerson(
        null,
        { hiveId: '1', firstName: '', lastName: 'Doe', dateOfBirth: '2000-01-01' },
        { driver }
      )
    ).rejects.toThrow(GraphQLError);
  });

  test('throws GraphQLError for empty lastName', async () => {
    const driver = createMockDriver();
    await expect(
      resolvers.Mutation.updatePerson(
        null,
        { hiveId: '1', firstName: 'John', lastName: ' ', dateOfBirth: '2000-01-01' },
        { driver }
      )
    ).rejects.toThrow(GraphQLError);
  });
});

