const { defaultFieldResolver, GraphQLError } = require('graphql');
const { mapSchema, getDirective, MapperKind } = require('@graphql-tools/utils');
const jwt = require('jsonwebtoken');

const CLEARANCE_ORDER = ['CL1','CL2','CL3','CL4','CL5','CL6','CL7','CL8','CL9','CLS','CLX'];

function checkClearance(required, actual) {
  const requiredIndex = CLEARANCE_ORDER.indexOf(required);
  const actualIndex = CLEARANCE_ORDER.indexOf(actual);
  return actualIndex >= requiredIndex;
}

function requiresCLDirective(directiveName) {
  return {
    typeDefs: `directive @${directiveName}(level: ClearanceLevel!) on FIELD_DEFINITION`,
    transformer: (schema) =>
      mapSchema(schema, {
        [MapperKind.OBJECT_FIELD]: (fieldConfig) => {
          const directive = getDirective(schema, fieldConfig, directiveName)?.[0];
          if (directive) {
            const { level } = directive;
            const originalResolve = fieldConfig.resolve || defaultFieldResolver;
            fieldConfig.resolve = async function (source, args, context, info) {
              const token = context.token;
              if (!token) {
                throw new GraphQLError('Missing authentication token', {
                  extensions: { code: 'FORBIDDEN' }
                });
              }
              try {
                const payload = jwt.verify(token.replace('Bearer ', ''), process.env.JWT_SECRET);
                const userClearance = payload.clearance;
                if (!checkClearance(level, userClearance)) {
                  throw new GraphQLError('Insufficient clearance', {
                    extensions: { code: 'FORBIDDEN' }
                  });
                }
              } catch (err) {
                if (err instanceof GraphQLError) {
                  throw err;
                }
                throw new GraphQLError('Invalid authentication token', {
                  extensions: { code: 'FORBIDDEN' }
                });
              }
              return originalResolve.call(this, source, args, context, info);
            };
            return fieldConfig;
          }
        }
      })
  };
}

module.exports = { requiresCLDirective };
