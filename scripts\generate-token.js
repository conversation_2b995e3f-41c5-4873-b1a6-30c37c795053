#!/usr/bin/env node

require('dotenv').config();
const jwt = require('jsonwebtoken');
const neo4j = require('neo4j-driver');

// Get environment variables
const {
  NEO4J_URI,
  NEO4J_DATABASE,
  NEO4J_USER,
  NEO4J_PASSWORD,
  JWT_SECRET
} = process.env;

// Validate required environment variables
const requiredEnvVars = {
  NEO4J_URI,
  NEO4J_DATABASE,
  NEO4J_USER,
  NEO4J_PASSWORD,
  JWT_SECRET
};

Object.entries(requiredEnvVars).forEach(([key, value]) => {
  if (!value) {
    console.error(`[ERROR] Missing required environment variable: ${key}`);
    process.exit(1);
  }
});

// Create Neo4j driver
const driver = neo4j.driver(
  NEO4J_URI,
  neo4j.auth.basic(NEO4J_USER, NEO4J_PASSWORD),
  { 
    database: NEO4J_DATABASE,
    connectionTimeout: 20000
  }
);

async function generateTokenForAgent(username) {
  const session = driver.session();
  try {
    // Find the agent by username
    const { records } = await session.run(
      `
        MATCH (a:Agent { username: $username })
        MATCH (a)-[:BACKED_BY]->(bp:Person)
        RETURN a {
          .username,
          .role,
          .clearance,
          hiveId: toString(a.hiveId),
          backingPerson: [bp { .hiveId, .firstName, .lastName }]
        } AS agent
      `,
      { username }
    );

    if (records.length === 0) {
      console.error(`[ERROR] Agent with username '${username}' not found`);
      return null;
    }

    const agent = records[0].get('agent');
    
    // Generate JWT token
    const token = jwt.sign(
      { 
        hiveId: agent.hiveId, 
        username: agent.username, 
        role: agent.role, 
        clearance: agent.clearance 
      },
      JWT_SECRET,
      { expiresIn: '12h' }
    );

    return { token, agent };
  } catch (error) {
    console.error('[ERROR] Failed to generate token:', error.message);
    return null;
  } finally {
    await session.close();
  }
}

async function listAgents() {
  const session = driver.session();
  try {
    const { records } = await session.run(
      `
        MATCH (a:Agent)-[:BACKED_BY]->(bp:Person)
        RETURN a {
          .username,
          .role,
          .clearance,
          hiveId: toString(a.hiveId),
          backingPerson: [bp { .hiveId, .firstName, .lastName }]
        } AS agent
        ORDER BY a.username
      `
    );

    return records.map(record => record.get('agent'));
  } catch (error) {
    console.error('[ERROR] Failed to list agents:', error.message);
    return [];
  } finally {
    await session.close();
  }
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args[0] === '--help' || args[0] === '-h') {
    console.log(`
Token Generator for Hive

Usage:
  npm run generate-token <username>     Generate token for specific agent
  npm run generate-token --list         List all available agents

Examples:
  npm run generate-token john_doe       Generate token for agent 'john_doe'
  npm run generate-token --list         Show all agents
    `);
    process.exit(0);
  }

  if (args[0] === '--list' || args[0] === '-l') {
    console.log('[INFO] Fetching agents...');
    const agents = await listAgents();
    
    if (agents.length === 0) {
      console.log('[INFO] No agents found in the database');
    } else {
      console.log('\n[INFO] Available agents:');
      console.log('------------------------');
      agents.forEach(agent => {
        const backingPerson = agent.backingPerson[0];
        console.log(`Username: ${agent.username}`);
        console.log(`Role: ${agent.role}`);
        console.log(`Clearance: ${agent.clearance}`);
        console.log(`Hive ID: ${agent.hiveId}`);
        if (backingPerson) {
          console.log(`Backing Person: ${backingPerson.firstName} ${backingPerson.lastName} (ID: ${backingPerson.hiveId})`);
        }
        console.log('------------------------');
      });
    }
  } else {
    const username = args[0];
    console.log(`[INFO] Generating token for agent: ${username}`);
    
    const result = await generateTokenForAgent(username);
    
    if (result) {
      const { token, agent } = result;
      const backingPerson = agent.backingPerson[0];
      
      console.log('\n[SUCCESS] Token generated successfully!');
      console.log('========================================');
      console.log(`Agent: ${agent.username}`);
      console.log(`Role: ${agent.role}`);
      console.log(`Clearance: ${agent.clearance}`);
      console.log(`Hive ID: ${agent.hiveId}`);
      if (backingPerson) {
        console.log(`Backing Person: ${backingPerson.firstName} ${backingPerson.lastName}`);
      }
      console.log('========================================');
      console.log(`Token (expires in 12h):`);
      console.log(token);
      console.log('========================================');
      console.log('\nYou can use this token in your Authorization header:');
      console.log(`Authorization: Bearer ${token}`);
    }
  }

  await driver.close();
}

// Handle errors and cleanup
process.on('SIGINT', async () => {
  console.log('\n[INFO] Shutting down...');
  await driver.close();
  process.exit(0);
});

process.on('unhandledRejection', async (error) => {
  console.error('[ERROR] Unhandled rejection:', error);
  await driver.close();
  process.exit(1);
});

// Run the script
main().catch(async (error) => {
  console.error('[ERROR] Script failed:', error);
  await driver.close();
  process.exit(1);
});

